#!/usr/bin/env bash
set -euo pipefail

############################################################
# 必填环境变量（可在脚本外 export，也可改成读取参数）：
#   ORG          组织名称，如 "contoso"
#   PROJECT      项目名称，如 "myproject"
#   REPO         仓库名称或 GUID
#   FILE         本地需加入 PR 的 JSON 文件路径，如 "./generated/settings.json"
# 可选：
#   BASE_BRANCH  目标分支，默认 "main"
#   NEW_BRANCH   新分支名，默认 "add-$(basename $FILE)-$(date +%s)"
############################################################
ORG="project-argos"
PROJECT="Mumford"
REPO="oai-engine-configs"
FILE="/Users/<USER>/Projects/oai-engine-configs/models/ledgers/swe-vsc-m15-s200-0728.json"

BASE_BRANCH=${BASE_BRANCH:-main}
NEW_BRANCH=${NEW_BRANCH:-add-$(basename "$FILE" .json)-$(date +%s)}

############################################################
# Step 0：取 Azure DevOps Bearer Token（有效期 1 小时）
# 说明：499b84ac-1321-427f-aa17-267ca6975798 是 Azure DevOps 在
#       Entra ID 中的资源 ID。:contentReference[oaicite:0]{index=0}
############################################################
TOKEN=$(az account get-access-token \
          --resource 499b84ac-1321-427f-aa17-267ca6975798 \
          --query accessToken -o tsv)

API="https://dev.azure.com/${ORG}/${PROJECT}/_apis"

############################################################
# Step 1：获取目标分支当前 HEAD commit ID
############################################################
HEAD=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  "${API}/git/repositories/${REPO}/refs/heads/${BASE_BRANCH}?api-version=7.1" |
  jq -r '.value[0].objectId')          # Refs API :contentReference[oaicite:1]{index=1}

echo "Base branch HEAD: $HEAD"

# 检查是否成功获取到 HEAD commit ID
if [ "$HEAD" = "null" ] || [ -z "$HEAD" ]; then
  echo "❌ 错误：无法获取分支 '${BASE_BRANCH}' 的 HEAD commit ID"
  echo "请检查分支名称是否正确，或者该分支是否存在"
  exit 1
fi

############################################################
# Step 2：一次 push 创建新分支并提交 JSON 文件
############################################################
FILE_PATH="/config/$(basename "$FILE")"
FILE_CONTENT=$(jq -Rs . < "$FILE")     # 压成 JSON 字符串

PUSH_RESULT=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -X POST "${API}/git/repositories/${REPO}/pushes?api-version=7.1" \
  -d @- <<EOF
{
  "refUpdates": [
    { "name": "refs/heads/${NEW_BRANCH}", "oldObjectId": "${HEAD}" }
  ],
  "commits": [
    {
      "comment": "Add $(basename "$FILE")",
      "changes": [
        {
          "changeType": "add",
          "item": { "path": "${FILE_PATH}" },
          "newContent": {
            "content": ${FILE_CONTENT},
            "contentType": "rawtext"
          }
        }
      ]
    }
  ]
}
EOF
# Pushes API 示例见官方文档 :contentReference[oaicite:2]{index=2}

)

# 检查push操作是否成功
if echo "$PUSH_RESULT" | jq -e '.message' > /dev/null 2>&1; then
  echo "❌ Push操作失败："
  echo "$PUSH_RESULT" | jq -r '.message'
  exit 1
fi

echo "✅ Pushed ${FILE_PATH} to ${NEW_BRANCH}"

############################################################
# Step 3：创建 Pull Request
############################################################
PR_JSON=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -X POST "${API}/git/repositories/${REPO}/pullrequests?api-version=7.1" \
  -d @- <<EOF
{
  "sourceRefName": "refs/heads/${NEW_BRANCH}",
  "targetRefName": "refs/heads/${BASE_BRANCH}",
  "title": "Add $(basename "$FILE")",
  "description": "Auto‑generated via script",
  "completionOptions": {
    "deleteSourceBranch": true
  }
}
EOF)
# PullRequests API :contentReference[oaicite:3]{index=3}

# 检查PR创建是否成功
if echo "$PR_JSON" | jq -e '.message' > /dev/null 2>&1; then
  echo "❌ 创建Pull Request失败："
  echo "$PR_JSON" | jq -r '.message'
  exit 1
fi

PR_ID=$(echo "$PR_JSON" | jq -r '.pullRequestId')
PR_URL=$(echo "$PR_JSON" | jq -r '.url')

# 检查是否成功获取到PR信息
if [ "$PR_ID" = "null" ] || [ -z "$PR_ID" ]; then
  echo "❌ 无法获取Pull Request ID，创建可能失败"
  echo "API响应："
  echo "$PR_JSON" | jq .
  exit 1
fi

echo "✅ Pull Request #${PR_ID} 创建成功！"
echo "🔗 ${PR_URL}"
